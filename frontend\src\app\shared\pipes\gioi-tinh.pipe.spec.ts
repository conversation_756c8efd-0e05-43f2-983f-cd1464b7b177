import { GioiTinhPipe } from './gioi-tinh.pipe';

describe('GioiTinhPipe', () => {
  let pipe: GioiTinhPipe;

  beforeEach(() => {
    pipe = new GioiTinhPipe();
  });

  it('should create', () => {
    expect(pipe).toBeTruthy();
  });

  it('should transform number 1 to "Nam"', () => {
    expect(pipe.transform(1)).toBe('Nam');
  });

  it('should transform number 0 to "Nữ"', () => {
    expect(pipe.transform(0)).toBe('Nữ');
  });

  it('should transform number 2 to "Nữ" (compatibility)', () => {
    expect(pipe.transform(2)).toBe('Nữ');
  });

  it('should transform string "1" to "Nam"', () => {
    expect(pipe.transform('1')).toBe('Nam');
  });

  it('should transform string "0" to "Nữ"', () => {
    expect(pipe.transform('0')).toBe('Nữ');
  });

  it('should transform string "2" to "Nữ" (compatibility)', () => {
    expect(pipe.transform('2')).toBe('Nữ');
  });

  it('should transform string "0" to "Nữ"', () => {
    expect(pipe.transform('0')).toBe('Nữ');
  });

  it('should transform string "Nam" to "Nam"', () => {
    expect(pipe.transform('Nam')).toBe('Nam');
  });

  it('should transform string "Nữ" to "Nữ"', () => {
    expect(pipe.transform('Nữ')).toBe('Nữ');
  });

  it('should transform string "nam" to "Nam"', () => {
    expect(pipe.transform('nam')).toBe('Nam');
  });

  it('should transform string "nữ" to "Nữ"', () => {
    expect(pipe.transform('nữ')).toBe('Nữ');
  });

  it('should transform string "nu" to "Nữ"', () => {
    expect(pipe.transform('nu')).toBe('Nữ');
  });

  it('should transform string "male" to "Nam"', () => {
    expect(pipe.transform('male')).toBe('Nam');
  });

  it('should transform string "female" to "Nữ"', () => {
    expect(pipe.transform('female')).toBe('Nữ');
  });

  it('should return empty string for null', () => {
    expect(pipe.transform(null)).toBe('');
  });

  it('should return empty string for undefined', () => {
    expect(pipe.transform(undefined)).toBe('');
  });

  it('should return empty string for empty string', () => {
    expect(pipe.transform('')).toBe('');
  });

  it('should return empty string for invalid number', () => {
    expect(pipe.transform(3)).toBe('');
    expect(pipe.transform(-1)).toBe('');
  });

  it('should return empty string for invalid string', () => {
    expect(pipe.transform('invalid')).toBe('');
    expect(pipe.transform('3')).toBe('');
    expect(pipe.transform('0')).toBe('');
  });
});
